import axios, { AxiosError } from 'axios';
import { ref } from 'vue';

// --- 配置 ---
const API_SERVER = '';
const API_BASE_URL = `${API_SERVER}/api/auth`;

// --- TypeScript 接口 ---

// 用户接口
export interface User {
  id: number;
  email: string | null;
  phone_number: string | null;
  is_admin?: boolean;
  email_verified?: boolean;
  phone_verified?: boolean;
}

// 统一的API响应格式
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
}

// 认证响应（包含用户数据）
interface AuthResponse extends ApiResponse<{ user: User }> {}

// 响应码常量
export const ResponseCode = {
  SUCCESS: 200000,  // 成功
  CREATED: 201000,  // 创建成功

  // 客户端错误 4xxxx
  BAD_REQUEST: 400000,  // 请求参数错误
  UNAUTHORIZED: 401000,  // 未授权
  FORBIDDEN: 403000,  // 禁止访问
  NOT_FOUND: 404000,  // 资源不存在
  CONFLICT: 409000,  // 资源冲突
  VALIDATION_ERROR: 422000,  // 验证错误

  // 服务器错误 5xxxx
  INTERNAL_ERROR: 500000,  // 服务器内部错误

  // 认证相关错误 4010xx
  INVALID_CREDENTIALS: 401001,  // 用户名或密码错误
  INVALID_OTP: 401002,  // 验证码错误
  OTP_EXPIRED: 401003,  // 验证码过期
  ACCOUNT_LOCKED: 401004,  // 账户被锁定

  // 注册相关错误 4090xx
  EMAIL_EXISTS: 409001,  // 邮箱已存在
  PHONE_EXISTS: 409002,  // 手机号已存在

  // 验证相关错误 4220xx
  MISSING_REQUIRED_FIELDS: 422001,  // 缺少必填字段
  INVALID_EMAIL_FORMAT: 422002,  // 邮箱格式错误
  INVALID_PHONE_FORMAT: 422003,  // 手机号格式错误
  INVALID_PASSWORD_FORMAT: 422004,  // 密码格式错误
  INVALID_TOKEN: 422005,  // 无效的令牌
} as const;

// 错误码到中文消息的映射
export const ErrorMessages: Record<number, string> = {
  [ResponseCode.BAD_REQUEST]: '请求参数错误',
  [ResponseCode.UNAUTHORIZED]: '未授权访问',
  [ResponseCode.FORBIDDEN]: '禁止访问',
  [ResponseCode.NOT_FOUND]: '资源不存在',
  [ResponseCode.CONFLICT]: '资源冲突',
  [ResponseCode.VALIDATION_ERROR]: '数据验证错误',
  [ResponseCode.INTERNAL_ERROR]: '服务器内部错误，请稍后重试',

  [ResponseCode.INVALID_CREDENTIALS]: '用户名或密码错误',
  [ResponseCode.INVALID_OTP]: '验证码错误',
  [ResponseCode.OTP_EXPIRED]: '验证码已过期',
  [ResponseCode.ACCOUNT_LOCKED]: '账户已被锁定',

  [ResponseCode.EMAIL_EXISTS]: '该邮箱已被注册',
  [ResponseCode.PHONE_EXISTS]: '该手机号已被注册',

  [ResponseCode.MISSING_REQUIRED_FIELDS]: '请填写所有必填字段',
  [ResponseCode.INVALID_EMAIL_FORMAT]: '邮箱格式不正确',
  [ResponseCode.INVALID_PHONE_FORMAT]: '手机号格式不正确',
  [ResponseCode.INVALID_PASSWORD_FORMAT]: '密码格式不正确',
  [ResponseCode.INVALID_TOKEN]: '无效的令牌',
};

// Registration
interface RegisterPayload {
  email?: string;
  phone_number?: string;
  password?: string;
}

// 使用密码登录
interface LoginPasswordPayload {
  identifier: string; // email or phone number
  password?: string;
  remember_me?: boolean;
}

// 发送OTP
interface SendOtpPayload {
  phone_number: string;
}

// 验证OTP
interface VerifyOtpPayload {
  phone_number: string;
  otp: string;
}

// 忘记密码请求
interface ForgotPasswordRequestPayload {
  identifier: string; // email or phone number
}

// 使用Token重置密码
interface ResetPasswordTokenPayload {
  token: string;
  new_password?: string;
}



// 通用错误响应（根据你的Flask错误结构调整）
interface ApiErrorResponse extends ApiResponse {
  // 错误响应也遵循统一格式
}

// --- 响应式状态（可选 - 用于演示目的） ---
const currentUser = ref<User | null>(null);
const isLoading = ref(false);
const errorMessage = ref<string | null>(null); // 错误信息

// --- API Service Functions ---

/**
 * 处理API错误，提取用户友好的错误信息。
 * @param error 来自Axios（或其他来源）的错误对象。
 * @param defaultMessage 如果无法提取特定信息时的默认消息。
 * @returns 用户友好的错误信息字符串。
 */
const handleApiError = (error: any, defaultMessage: string = '发生未知错误'): string => {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<ApiErrorResponse>;
    if (axiosError.response && axiosError.response.data) {
      const responseData = axiosError.response.data;

      // 如果有自定义错误码，使用对应的中文消息
      if (responseData.code && ErrorMessages[responseData.code]) {
        return ErrorMessages[responseData.code];
      }

      // 否则使用服务器返回的消息
      if (responseData.message) {
        return responseData.message;
      }
    }
    return axiosError.message;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return defaultMessage;
};

/**
 * 注册新用户。
 * @param payload - 邮箱（可选），手机号（可选），密码。
 */
async function registerUser(payload: RegisterPayload): Promise<User | null> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    // Basic client-side validation
    if (!(payload.email || payload.phone_number) || !payload.password) {
      throw new Error('Email or phone number, and password are required.');
    }
    if (payload.email && !/\S+@\S+\.\S+/.test(payload.email)) {
        throw new Error('Invalid email format.');
    }

    const response = await axios.post<AuthResponse>(`${API_BASE_URL}/register`, payload);
    console.log('Registration successful:', response.data.message);
    if (response.data.data && response.data.data.user) {
      currentUser.value = response.data.data.user; // Optionally update current user state
      return response.data.data.user;
    }
    return null;
  } catch (error) {
    errorMessage.value = handleApiError(error, '注册失败');
    console.error('Registration error:', errorMessage.value);
    return null;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 使用标识符（邮箱/手机号）和密码登录用户。
 * @param payload - 标识符和密码。
 */
async function loginWithPassword(payload: LoginPasswordPayload): Promise<User | null> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    if (!payload.identifier || !payload.password) {
      throw new Error('Identifier and password are required.');
    }
    const response = await axios.post<AuthResponse>(`${API_BASE_URL}/login/password`, payload);
    console.log('Login successful:', response.data.message);
    if (response.data.data && response.data.data.user) {
      currentUser.value = response.data.data.user;
      return response.data.data.user;
    }
    return null;
  } catch (error) {
    errorMessage.value = handleApiError(error, '登录失败');
    console.error('Login error:', errorMessage.value);
    return null;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 向用户的手机号发送OTP。
 * @param payload - 手机号。
 */
async function sendLoginOtp(payload: SendOtpPayload): Promise<boolean> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    if (!payload.phone_number) {
      throw new Error('Phone number is required.');
    }
    const response = await axios.post<ApiResponse>(`${API_BASE_URL}/login/phone/send-otp`, payload);
    console.log('Send OTP response:', response.data.message);
    return response.data.code === ResponseCode.SUCCESS;
  } catch (error) {
    errorMessage.value = handleApiError(error, '验证码发送失败');
    console.error('Send OTP error:', errorMessage.value);
    return false;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 验证OTP并登录用户。
 * @param payload - 手机号和OTP。
 */
async function verifyLoginOtp(payload: VerifyOtpPayload): Promise<User | null> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    if (!payload.phone_number || !payload.otp) {
      throw new Error('Phone number and OTP are required.');
    }
    const response = await axios.post<AuthResponse>(`${API_BASE_URL}/login/phone/verify-otp`, payload);
    console.log('OTP verification successful:', response.data.message);
    if (response.data.data && response.data.data.user) {
      currentUser.value = response.data.data.user;
      return response.data.data.user;
    }
    return null;
  } catch (error) {
    errorMessage.value = handleApiError(error, '验证码验证失败');
    console.error('OTP verification error:', errorMessage.value);
    return null;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 注销当前用户。
 */
async function logoutUser(): Promise<boolean> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    const response = await axios.post<ApiResponse>(`${API_BASE_URL}/logout`);
    console.log('Logout successful:', response.data.message);
    currentUser.value = null;
    return response.data.code === ResponseCode.SUCCESS;
  } catch (error) {
    // Logout might fail if the session is already invalid or network issue
    // Forcing logout on client-side regardless of server error might be a strategy
    currentUser.value = null; // Clear user state on client even if server call fails
    errorMessage.value = handleApiError(error, '退出登录失败，但已在本地登出');
    console.error('Logout error:', errorMessage.value);
    return false; // Indicate server logout might have failed
  } finally {
    isLoading.value = false;
  }
}

/**
 * 获取当前认证用户的详细信息。
 */
async function fetchCurrentUser(): Promise<User | null> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    const response = await axios.get<ApiResponse<{ user: User }>>(`${API_BASE_URL}/me`);
    console.log('Fetched current user:', response.data.data?.user);
    if (response.data.data?.user) {
      currentUser.value = response.data.data.user;
      return response.data.data.user;
    }
    return null;
  } catch (error) {
     // If /me returns 401 (Unauthorized), it means user is not logged in
    if (axios.isAxiosError(error) && error.response?.status === 401) {
        currentUser.value = null; // Ensure local state reflects not logged in
        errorMessage.value = '您尚未登录';
    } else {
        errorMessage.value = handleApiError(error, '获取用户详情失败');
    }
    console.error('Fetch current user error:', errorMessage.value);
    return null;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 为给定的标识符（邮箱/手机号）请求密码重置。
 * @param payload - 标识符。
 */
async function requestPasswordReset(payload: ForgotPasswordRequestPayload): Promise<boolean> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    if (!payload.identifier) {
      throw new Error('Email or phone number is required.');
    }
    const response = await axios.post<ApiResponse>(`${API_BASE_URL}/password/forgot/request`, payload);
    console.log('Password reset request response:', response.data.message);
    // Usually, you don't get direct feedback if the user exists for security reasons
    // The message from backend "If your account exists..." is good.
    return response.data.code === ResponseCode.SUCCESS;
  } catch (error) {
    errorMessage.value = handleApiError(error, '密码重置请求失败');
    console.error('Password reset request error:', errorMessage.value);
    return false;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 使用Token重置密码。
 * @param payload - Token和新密码。
 */
async function resetPasswordWithToken(payload: ResetPasswordTokenPayload): Promise<boolean> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    if (!payload.token || !payload.new_password) {
      throw new Error('Token and new password are required.');
    }
    const response = await axios.post<ApiResponse>(`${API_BASE_URL}/password/reset/token`, payload);
    console.log('Password reset successful:', response.data.message);
    return response.data.code === ResponseCode.SUCCESS;
  } catch (error) {
    errorMessage.value = handleApiError(error, '密码重置失败');
    console.error('Password reset error:', errorMessage.value);
    return false;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 注销账号 - 删除用户账号及其所有相关数据
 */
async function deleteAccount(): Promise<boolean> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    const response = await axios.delete<ApiResponse>(`${API_BASE_URL}/account`);
    console.log('Account deleted successfully:', response.data.message);
    currentUser.value = null; // 清除本地用户状态
    return response.data.code === ResponseCode.SUCCESS;
  } catch (error) {
    errorMessage.value = handleApiError(error, '注销账号失败');
    console.error('Delete account error:', errorMessage.value);
    return false;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 清空所有对话 - 删除当前用户的所有对话
 */
async function clearAllConversations(): Promise<{ deleted_count: number } | null> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    const response = await axios.delete<ApiResponse<{ deleted_count: number }>>(`${API_BASE_URL}/conversations`);
    console.log('Conversations cleared successfully:', response.data.message);
    return response.data.data || null;
  } catch (error) {
    errorMessage.value = handleApiError(error, '清空对话失败');
    console.error('Clear conversations error:', errorMessage.value);
    return null;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 登出所有设备 - 使所有设备上的会话失效
 */
async function logoutAllDevices(): Promise<boolean> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    const response = await axios.post<ApiResponse>(`${API_BASE_URL}/logout-all`);
    console.log('Logged out from all devices:', response.data.message);
    currentUser.value = null; // 清除本地用户状态
    return response.data.code === ResponseCode.SUCCESS;
  } catch (error) {
    errorMessage.value = handleApiError(error, '登出所有设备失败');
    console.error('Logout all devices error:', errorMessage.value);
    return false;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 导出所有历史对话 - 下载用户的所有对话数据
 */
async function exportConversations(): Promise<boolean> {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    const response = await axios.get(`${API_BASE_URL}/export/conversations`, {
      responseType: 'blob' // 重要：指定响应类型为blob以处理文件下载
    });

    // 创建下载链接
    const blob = new Blob([response.data], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // 从响应头获取文件名，如果没有则使用默认名称
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'conversations_export.json';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    console.log('Conversations exported successfully');
    return true;
  } catch (error) {
    errorMessage.value = handleApiError(error, '导出对话失败');
    console.error('Export conversations error:', errorMessage.value);
    return false;
  } finally {
    isLoading.value = false;
  }
}

export {
  registerUser,
  loginWithPassword,
  sendLoginOtp,
  verifyLoginOtp,
  logoutUser,
  fetchCurrentUser,
  requestPasswordReset,
  resetPasswordWithToken,
  deleteAccount,
  clearAllConversations,
  logoutAllDevices,
  exportConversations,
  currentUser,
  isLoading,
  errorMessage
};