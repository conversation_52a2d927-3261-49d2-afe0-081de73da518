from flask import request, jsonify, current_app, make_response
from flask_login import login_user, logout_user, login_required, current_user
from app import db, login_manager # Import from top-level app package
from app.models import User, Conversation
from app.auth import bp
import json
from datetime import datetime

# 自定义响应码常量
class ResponseCode:
    SUCCESS = 200000  # 成功
    CREATED = 201000  # 创建成功

    # 客户端错误 4xxxx
    BAD_REQUEST = 400000  # 请求参数错误
    UNAUTHORIZED = 401000  # 未授权
    FORBIDDEN = 403000  # 禁止访问
    NOT_FOUND = 404000  # 资源不存在
    CONFLICT = 409000  # 资源冲突
    VALIDATION_ERROR = 422000  # 验证错误

    # 服务器错误 5xxxx
    INTERNAL_ERROR = 500000  # 服务器内部错误

    # 认证相关错误 4010xx
    INVALID_CREDENTIALS = 401001  # 用户名或密码错误
    INVALID_OTP = 401002  # 验证码错误
    OTP_EXPIRED = 401003  # 验证码过期
    ACCOUNT_LOCKED = 401004  # 账户被锁定

    # 注册相关错误 4090xx
    EMAIL_EXISTS = 409001  # 邮箱已存在
    PHONE_EXISTS = 409002  # 手机号已存在

    # 验证相关错误 4220xx
    MISSING_REQUIRED_FIELDS = 422001  # 缺少必填字段
    INVALID_EMAIL_FORMAT = 422002  # 邮箱格式错误
    INVALID_PHONE_FORMAT = 422003  # 手机号格式错误
    INVALID_PASSWORD_FORMAT = 422004  # 密码格式错误
    INVALID_TOKEN = 422005  # 无效的令牌

def create_response(code=ResponseCode.SUCCESS, message="操作成功", data=None):
    """创建统一的响应格式，所有响应都使用HTTP 200状态码"""
    response_data = {
        "code": code,
        "message": message
    }
    if data is not None:
        response_data["data"] = data
    return jsonify(response_data), 200

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

@bp.route('/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        email = data.get('email')
        phone_number = data.get('phone_number')
        password = data.get('password')

        # 验证必填字段
        if not (email or phone_number) or not password:
            return create_response(
                code=ResponseCode.MISSING_REQUIRED_FIELDS,
                message="邮箱或手机号和密码为必填项"
            )

        # 验证邮箱格式
        if email and not validate_email_format(email):
            return create_response(
                code=ResponseCode.INVALID_EMAIL_FORMAT,
                message="邮箱格式不正确"
            )

        # 验证手机号格式
        if phone_number and not validate_phone_format(phone_number):
            return create_response(
                code=ResponseCode.INVALID_PHONE_FORMAT,
                message="手机号格式不正确"
            )

        # 检查邮箱是否已存在
        if email and User.query.filter_by(email=email).first():
            return create_response(
                code=ResponseCode.EMAIL_EXISTS,
                message="该邮箱已被注册"
            )

        # 检查手机号是否已存在
        if phone_number and User.query.filter_by(phone_number=phone_number).first():
            return create_response(
                code=ResponseCode.PHONE_EXISTS,
                message="该手机号已被注册"
            )

        # 创建用户
        user = User(email=email, phone_number=phone_number)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()

        return create_response(
            code=ResponseCode.CREATED,
            message="用户注册成功",
            data={
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'phone_number': user.phone_number
                }
            }
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Registration error: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="注册失败，请稍后重试"
        )

def validate_email_format(email):
    """验证邮箱格式"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone_format(phone_number):
    """验证手机号格式（中国大陆手机号）"""
    import re
    pattern = r'^1[3-9]\d{9}$'
    return re.match(pattern, phone_number) is not None

@bp.route('/login/password', methods=['POST'])
def login_password():
    try:
        data = request.get_json()
        identifier = data.get('identifier')
        password = data.get('password')

        # 验证必填字段
        if not identifier or not password:
            return create_response(
                code=ResponseCode.MISSING_REQUIRED_FIELDS,
                message="用户名和密码为必填项"
            )

        # 查找用户
        user = User.query.filter((User.email == identifier) | (User.phone_number == identifier)).first()

        # 验证用户和密码
        if user and user.check_password(password):
            login_user(user, remember=data.get('remember_me', False))
            return create_response(
                code=ResponseCode.SUCCESS,
                message="登录成功",
                data={
                    'user': {
                        'id': user.id,
                        'email': user.email,
                        'phone_number': user.phone_number,
                        'is_admin': user.is_admin
                    }
                }
            )

        return create_response(
            code=ResponseCode.INVALID_CREDENTIALS,
            message="用户名或密码错误"
        )

    except Exception as e:
        current_app.logger.error(f"Login error: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="登录失败，请稍后重试"
        )

@bp.route('/login/phone/send-otp', methods=['POST'])
def send_otp():
    try:
        data = request.get_json()
        phone_number = data.get('phone_number')

        # 验证必填字段
        if not phone_number:
            return create_response(
                code=ResponseCode.MISSING_REQUIRED_FIELDS,
                message="手机号为必填项"
            )

        # 验证手机号格式
        if not validate_phone_format(phone_number):
            return create_response(
                code=ResponseCode.INVALID_PHONE_FORMAT,
                message="手机号格式不正确"
            )

        # TODO: Implement OTP sending logic
        # Generate OTP, store hash, send via SMS service
        return create_response(
            code=ResponseCode.SUCCESS,
            message="验证码发送成功"
        )

    except Exception as e:
        current_app.logger.error(f"Send OTP error: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="验证码发送失败，请稍后重试"
        )

@bp.route('/login/phone/verify-otp', methods=['POST'])
def verify_otp():
    try:
        data = request.get_json()
        phone_number = data.get('phone_number')
        otp = data.get('otp')

        # 验证必填字段
        if not phone_number or not otp:
            return create_response(
                code=ResponseCode.MISSING_REQUIRED_FIELDS,
                message="手机号和验证码为必填项"
            )

        # 验证手机号格式
        if not validate_phone_format(phone_number):
            return create_response(
                code=ResponseCode.INVALID_PHONE_FORMAT,
                message="手机号格式不正确"
            )

        # TODO: Implement OTP verification logic
        # 这里应该验证OTP是否正确，暂时跳过验证
        # if not verify_otp_code(phone_number, otp):
        #     return create_response(
        #         code=ResponseCode.INVALID_OTP,
        #         message="验证码错误或已过期"
        #     )

        # 查找或创建用户
        user = User.query.filter_by(phone_number=phone_number).first()
        if not user:
            user = User(phone_number=phone_number)
            db.session.add(user)
            db.session.commit()

        login_user(user)
        return create_response(
            code=ResponseCode.SUCCESS,
            message="登录成功",
            data={
                'user': {
                    'id': user.id,
                    'phone_number': user.phone_number
                }
            }
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Verify OTP error: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="验证码验证失败，请稍后重试"
        )

@bp.route('/logout', methods=['POST'])
@login_required
def logout():
    try:
        logout_user()
        return create_response(
            code=ResponseCode.SUCCESS,
            message="退出登录成功"
        )
    except Exception as e:
        current_app.logger.error(f"Logout error: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="退出登录失败，请稍后重试"
        )

@bp.route('/me', methods=['GET'])
@login_required
def get_current_user():
    try:
        return create_response(
            code=ResponseCode.SUCCESS,
            message="获取用户信息成功",
            data={
                'user': {
                    'id': current_user.id,
                    'email': current_user.email,
                    'phone_number': current_user.phone_number,
                    'email_verified': current_user.email_verified,
                    'phone_verified': current_user.phone_verified,
                    'is_admin': current_user.is_admin
                }
            }
        )
    except Exception as e:
        current_app.logger.error(f"Get current user error: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="获取用户信息失败"
        )

@bp.route('/password/forgot/request', methods=['POST'])
def forgot_password_request():
    try:
        data = request.get_json()
        identifier = data.get('identifier')

        # 验证必填字段
        if not identifier:
            return create_response(
                code=ResponseCode.MISSING_REQUIRED_FIELDS,
                message="邮箱或手机号为必填项"
            )

        # 查找用户（出于安全考虑，不论用户是否存在都返回相同消息）
        user = User.query.filter((User.email == identifier) | (User.phone_number == identifier)).first()
        if user:
            # TODO: Implement password reset token generation and sending
            pass

        return create_response(
            code=ResponseCode.SUCCESS,
            message="如果您的账户存在，密码重置说明已发送"
        )

    except Exception as e:
        current_app.logger.error(f"Forgot password request error: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="密码重置请求失败，请稍后重试"
        )

@bp.route('/password/reset/token', methods=['POST'])
def reset_password_token():
    try:
        data = request.get_json()
        token = data.get('token')
        new_password = data.get('new_password')

        # 验证必填字段
        if not token or not new_password:
            return create_response(
                code=ResponseCode.MISSING_REQUIRED_FIELDS,
                message="令牌和新密码为必填项"
            )

        # TODO: Implement token verification and password update
        # if not verify_reset_token(token):
        #     return create_response(
        #         code=ResponseCode.INVALID_TOKEN,
        #         message="无效或已过期的重置令牌"
        #     )

        return create_response(
            code=ResponseCode.SUCCESS,
            message="密码重置成功"
        )

    except Exception as e:
        current_app.logger.error(f"Reset password error: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="密码重置失败，请稍后重试"
        )


# 新增的四个API端点

@bp.route('/account', methods=['DELETE'])
@login_required
def delete_account():
    """
    注销账号 - 删除用户账号及其所有相关数据
    """
    try:
        user_id = current_user.id
        user_identifier = current_user.email or current_user.phone_number

        # 先登出当前用户
        logout_user()

        # 删除用户（级联删除会自动删除相关的对话和消息）
        user = User.query.get(user_id)
        if user:
            db.session.delete(user)
            db.session.commit()
            current_app.logger.info(f"User account deleted: {user_identifier}")

        return create_response(
            code=ResponseCode.SUCCESS,
            message="账号注销成功"
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting account: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="账号注销失败，请稍后重试"
        )


@bp.route('/conversations', methods=['DELETE'])
@login_required
def clear_all_conversations():
    """
    清空所有对话 - 删除当前用户的所有对话
    """
    try:
        # 删除当前用户的所有对话（级联删除会自动删除相关的消息节点）
        conversations = Conversation.query.filter_by(user_id=current_user.id).all()
        conversation_count = len(conversations)

        for conversation in conversations:
            db.session.delete(conversation)

        db.session.commit()
        current_app.logger.info(f"Cleared {conversation_count} conversations for user {current_user.id}")

        return create_response(
            code=ResponseCode.SUCCESS,
            message="所有对话清空成功",
            data={
                'deleted_count': conversation_count
            }
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error clearing conversations: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="清空对话失败，请稍后重试"
        )


@bp.route('/logout-all', methods=['POST'])
@login_required
def logout_all_devices():
    """
    登出所有设备 - 使所有设备上的会话失效
    注意：由于Flask-Login基于session的特性，这里主要是登出当前会话
    如果需要真正的多设备管理，需要实现基于token的认证系统
    """
    try:
        user_identifier = current_user.email or current_user.phone_number

        # 登出当前用户
        logout_user()

        # 在实际的多设备管理中，这里应该：
        # 1. 使所有JWT token失效（如果使用JWT）
        # 2. 清除所有设备的session记录
        # 3. 通知其他设备登出

        current_app.logger.info(f"User logged out from all devices: {user_identifier}")

        return create_response(
            code=ResponseCode.SUCCESS,
            message="已从所有设备登出"
        )

    except Exception as e:
        current_app.logger.error(f"Error logging out from all devices: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="登出所有设备失败，请稍后重试"
        )


@bp.route('/export/conversations', methods=['GET'])
@login_required
def export_conversations():
    """
    导出所有历史对话 - 导出用户的所有对话数据为JSON格式
    """
    try:
        # 获取当前用户的所有对话
        conversations = Conversation.query.filter_by(user_id=current_user.id).order_by(Conversation.created_at.desc()).all()

        export_data = {
            'user_info': {
                'id': current_user.id,
                'email': current_user.email,
                'phone_number': current_user.phone_number,
                'export_date': datetime.now(datetime.timezone.utc).isoformat()
            },
            'conversations': []
        }

        for conversation in conversations:
            # 获取对话的完整数据
            conversation_data = conversation.to_dict_full()
            export_data['conversations'].append(conversation_data)

        # 创建响应
        response_data = json.dumps(export_data, ensure_ascii=False, indent=2)
        response = make_response(response_data)
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename="conversations_export_{current_user.id}_{datetime.now(datetime.timezone.utc).strftime("%Y%m%d_%H%M%S")}.json"'

        current_app.logger.info(f"Exported {len(conversations)} conversations for user {current_user.id}")

        return response

    except Exception as e:
        current_app.logger.error(f"Error exporting conversations: {str(e)}")
        return create_response(
            code=ResponseCode.INTERNAL_ERROR,
            message="导出对话失败，请稍后重试"
        )